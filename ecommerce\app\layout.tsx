import type { <PERSON>ada<PERSON> } from "next";
import localFont from "next/font/local";
import "./globals.css";
import "../styles/product-card.css";
import { AuthProvider } from "../provider/AuthProvider";
import { ThemeProvider } from "../provider/ThemeProvider";
import JsonLdWrapper from "../components/utils/JsonLdWrapper";
import ConsentBanner from "../components/privacy/ConsentBanner";
import SecureCartProvider from "../provider/SecureCartProvider";

const geistSans = localFont({
  src: "./fonts/GeistVF.woff",
  variable: "--font-geist-sans",
  weight: "100 900",
});
const geistMono = localFont({
  src: "./fonts/GeistMonoVF.woff",
  variable: "--font-geist-mono",
  weight: "100 900",
});

export const metadata: Metadata = {
  title: "Triumph Enterprises | Premium Hardware & Security Solutions",
  description: "Triumph Enterprises offers high-quality hardware, security products, and home solutions. Shop our wide range of door locks, digital safes, and more.",
  keywords: "hardware, security products, door locks, digital safes, home solutions, triumph enterprises",
  metadataBase: new URL(process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000'),
  alternates: {
    canonical: '/',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-image-preview': 'large',
      'max-video-preview': -1,
      'max-snippet': -1,
    },
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: '/',
    siteName: 'Triumph Enterprises',
    title: 'Triumph Enterprises | Premium Hardware & Security Solutions',
    description: 'Triumph Enterprises offers high-quality hardware, security products, and home solutions. Shop our wide range of door locks, digital safes, and more.',
    images: [
      {
        url: '/logo/og-image.png',
        width: 1200,
        height: 630,
        alt: 'Triumph Enterprises Logo',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Triumph Enterprises | Premium Hardware & Security Solutions',
    description: 'Triumph Enterprises offers high-quality hardware, security products, and home solutions. Shop our wide range of door locks, digital safes, and more.',
    images: ['/logo/twitter-card.png'],
    creator: '@triumphenterprises',
  },
  icons: {
    icon: [
      { url: '/favicon.ico' },
      { url: '/logo/favicon-16x16.png', sizes: '16x16', type: 'image/png' },
      { url: '/logo/favicon-32x32.png', sizes: '32x32', type: 'image/png' },
      { url: '/logo/favicon-48x48.png', sizes: '48x48', type: 'image/png' },
      { url: '/logo/favicon-64x64.png', sizes: '64x64', type: 'image/png' }
    ],
    apple: [
      { url: '/logo/apple-touch-icon.png', sizes: '180x180', type: 'image/png' }
    ],
    other: [
      {
        rel: 'mask-icon',
        url: '/logo/logo-512x512.png',
        color: '#2ECC71'
      }
    ]
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning={true}>
      <head>
        <link rel="manifest" href="/manifest.json" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <meta name="theme-color" content="#2ECC71" />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <JsonLdWrapper />
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <AuthProvider>
            <SecureCartProvider>
              {children}
              <ConsentBanner />
            </SecureCartProvider>
          </AuthProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
