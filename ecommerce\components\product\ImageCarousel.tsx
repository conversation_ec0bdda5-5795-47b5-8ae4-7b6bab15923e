import React, { useState } from "react";
import { EmblaOptionsType } from "embla-carousel";
import Carousel, { <PERSON><PERSON><PERSON>, <PERSON>lider<PERSON><PERSON><PERSON>, ThumsSlider } from "../ui/carousel";
import Image from "next/image";
import { MotionConfig } from "framer-motion";
import { getImageUrl } from "@/utils/imageUtils";
import { ImageModal } from "../ui/Modal";

const transition = {
  type: "spring",
  duration: 0.4,
};

interface ImageCarouselProps {
  productImage: any[];
  brand?: {
    id?: number;
    name: string;
    image_url?: string;
    image?: string;
  } | string;
}

export default function ImageCarousel({ productImage, brand }: ImageCarouselProps) {
  const OPTIONS: EmblaOptionsType = { loop: true };
  const [isMediaModalOpen, setIsMediaModalOpen] = useState<string | undefined>(undefined);

  return (
    <MotionConfig transition={transition}>
      <div className="w-full bg-background mx-auto relative">
        {/* Brand Badge - Image only positioned at the top right of the carousel for better visibility */}
        {brand && typeof brand !== 'string' && (brand?.image_url || brand?.image) && (
          <div className="absolute top-3 right-3 z-20">
            <div className="w-12 h-12 sm:w-14 sm:h-14 overflow-hidden rounded-lg border-2 border-white shadow-lg bg-white flex items-center justify-center p-1">
              <img
                src={brand?.image_url || `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000'}${brand?.image}`}
                alt={`${brand?.name} logo`}
                className="max-w-full max-h-full object-contain"
                onError={(e) => {
                  // Hide the image on error
                  const imgElement = e.currentTarget as HTMLImageElement;
                  imgElement.style.display = 'none';
                }}
              />
            </div>
          </div>
        )}
        <Carousel options={OPTIONS} className="relative" isAutoPlay={true}>
          <SliderContainer className="gap-2">
            {productImage.map((image: any) => (
              <Slider
                key={image.id}
                className="xl:h-[500px] lg:h-[450px] md:h-[400px] sm:h-[350px] h-[300px] w-full"
                thumnailSrc={getImageUrl(image?.image)}
              >
                <div className="w-full h-full flex items-center justify-center p-2">
                  <Image
                    onClick={() => {
                      setIsMediaModalOpen(getImageUrl(image?.image));
                    }}
                    src={getImageUrl(image?.image)}
                    width={1400}
                    height={800}
                    alt="image"
                    className="max-h-full max-w-full object-contain rounded-lg cursor-pointer transition-transform duration-200 hover:scale-105"
                    style={{ width: 'auto', height: 'auto' }}
                  />
                </div>
              </Slider>
            ))}
          </SliderContainer>
          <ThumsSlider />
        </Carousel>

        {/* Image Modal */}
        <ImageModal
          isOpen={!!isMediaModalOpen}
          onClose={() => setIsMediaModalOpen(undefined)}
          imageSrc={isMediaModalOpen || ''}
          imageAlt="Product image"
        />
      </div>
    </MotionConfig>
  );
}