// @ts-nocheck
"use client";
import React, { useState, useId } from "react";
import { motion, MotionConfig } from "framer-motion";
import { ImageModal } from "./Modal";

interface IMediaModal {
  imgSrc?: string;
  className?: string;
}
const transition = {
  type: "spring",
  duration: 0.4,
};
export function MediaModal({ imgSrc, className }: IMediaModal) {
  const [isMediaModalOpen, setIsMediaModalOpen] = useState(false);
  return (
    <>
      <MotionConfig transition={transition}>
        <>
          <motion.div
            // @ts-ignore
            className="w-full h-full flex relative  flex-col overflow-hidden border    dark:bg-black bg-gray-300 hover:bg-gray-200 dark:hover:bg-gray-950"
            layoutId={`dialog-${uniqueId}`}
            style={{
              borderRadius: "12px",
            }}
            onClick={() => {
              setIsMediaModalOpen(true);
            }}
          >
            {imgSrc && (
              <motion.div
                layoutId={`dialog-img-${uniqueId}`}
                className="w-full h-full"
              >
                <img
                  src={imgSrc}
                  alt="A desk lamp designed by <PERSON><PERSON> Wilfrid Buquet in 1925. It features a double-arm design and is made from nickel-plated brass, aluminium and varnished wood."
                  className=" w-full object-cover h-full"
                />
              </motion.div>
            )}
          </motion.div>
        </>
        {/* Image Modal */}
        <ImageModal
          isOpen={isMediaModalOpen}
          onClose={() => setIsMediaModalOpen(false)}
          imageSrc={imgSrc || ''}
          imageAlt="Media image"
        />
      </MotionConfig>
    </>
  );
}
