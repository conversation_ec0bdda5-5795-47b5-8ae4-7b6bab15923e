import { useEffect, useRef } from 'react';

interface UseModalScrollLockOptions {
  isOpen: boolean;
  lockClass?: string;
}

/**
 * Custom hook to manage body scroll lock for modals
 * Provides Amazon-like modal behavior where background content doesn't scroll
 */
export function useModalScrollLock({ isOpen, lockClass = 'modal-open' }: UseModalScrollLockOptions) {
  const scrollPositionRef = useRef<number>(0);
  const isLockedRef = useRef<boolean>(false);

  useEffect(() => {
    const body = document.body;
    const html = document.documentElement;

    if (isOpen && !isLockedRef.current) {
      // Store current scroll position
      scrollPositionRef.current = window.pageYOffset || html.scrollTop || body.scrollTop || 0;
      
      // Apply scroll lock
      body.classList.add(lockClass);
      body.style.top = `-${scrollPositionRef.current}px`;
      
      // Prevent scrolling on touch devices
      body.style.position = 'fixed';
      body.style.width = '100%';
      body.style.overflow = 'hidden';
      
      // Prevent scroll on html element as well
      html.style.overflow = 'hidden';
      
      isLockedRef.current = true;
    } else if (!isOpen && isLockedRef.current) {
      // Remove scroll lock
      body.classList.remove(lockClass);
      body.style.removeProperty('top');
      body.style.removeProperty('position');
      body.style.removeProperty('width');
      body.style.removeProperty('overflow');
      
      // Restore scroll on html element
      html.style.removeProperty('overflow');
      
      // Restore scroll position
      window.scrollTo(0, scrollPositionRef.current);
      
      isLockedRef.current = false;
    }

    // Cleanup function
    return () => {
      if (isLockedRef.current) {
        body.classList.remove(lockClass);
        body.style.removeProperty('top');
        body.style.removeProperty('position');
        body.style.removeProperty('width');
        body.style.removeProperty('overflow');
        html.style.removeProperty('overflow');
        window.scrollTo(0, scrollPositionRef.current);
        isLockedRef.current = false;
      }
    };
  }, [isOpen, lockClass]);

  // Handle escape key
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isOpen) {
        // This will be handled by the component using this hook
        return;
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen]);
}

/**
 * Utility function to check if any modal is currently open
 */
export function isAnyModalOpen(): boolean {
  return document.body.classList.contains('modal-open');
}

/**
 * Utility function to force close all modals (emergency cleanup)
 */
export function forceCloseAllModals(): void {
  const body = document.body;
  const html = document.documentElement;
  
  body.classList.remove('modal-open');
  body.style.removeProperty('top');
  body.style.removeProperty('position');
  body.style.removeProperty('width');
  body.style.removeProperty('overflow');
  html.style.removeProperty('overflow');
}
